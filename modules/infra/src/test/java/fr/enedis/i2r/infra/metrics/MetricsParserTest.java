package fr.enedis.i2r.infra.metrics;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class MetricsParserTest {

    @Test
    void parseJsonLine_convertit_ligne_json_valide() {
        String jsonLine = "{\"name\":\"test\",\"value\":123}";

        var result = MetricsParser.parseServingCell(jsonLine);

        assertThat(result).isPresent();
    }

    @Test
    void parseJsonLine_gere_ligne_invalide() {
        String invalidJson = "invalid json";

        var result = MetricsParser.parseJsonLine(invalidJson);

        assertThat(result).isEmpty();
    }
}
